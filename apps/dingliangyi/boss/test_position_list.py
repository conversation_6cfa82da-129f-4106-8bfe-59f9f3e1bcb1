#!/usr/bin/env python3
"""
测试重构后的position_list.py功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 直接导入position数据进行测试
from position import positions, hot_city, conditions

def extract_all_positions():
    """从嵌套的positions结构中提取所有职位的平铺列表"""
    all_positions = []

    def extract_positions_recursive(position_list):
        for item in position_list:
            if 'subLevelModelList' in item:
                extract_positions_recursive(item['subLevelModelList'])
            else:
                all_positions.append({'code': item['code'], 'name': item['name']})

    extract_positions_recursive(positions)
    return all_positions

def generate_main_combinations():
    """生成主搜索条件组合：position × city × jobType"""
    all_positions = extract_all_positions()
    job_types = [jt for jt in conditions['jobTypeList'] if jt['code'] in [1901, 1903]]

    main_combinations = []

    for position in all_positions[:5]:  # 只取前5个职位进行测试
        for city in hot_city[:3]:  # 只取前3个城市进行测试
            for job_type in job_types:
                combination = {
                    'position': position['code'],
                    'city': city['code'],
                    'jobType': job_type['code'],
                    'experience': '',
                    'salary': '',
                    'degree': '',
                    'stage': '',
                    'scale': '',
                }
                main_combinations.append(combination)

    return main_combinations

def test_extract_all_positions():
    """测试提取所有职位功能"""
    print("=== 测试提取所有职位 ===")
    all_positions = extract_all_positions()
    print(f"提取到 {len(all_positions)} 个职位")
    print("前10个职位:")
    for i, pos in enumerate(all_positions[:10]):
        print(f"  {i+1}. {pos['name']} (code: {pos['code']})")
    print()

def test_generate_main_combinations():
    """测试生成主搜索条件组合"""
    print("=== 测试生成主搜索条件组合 ===")
    main_combinations = generate_main_combinations()
    print(f"生成 {len(main_combinations)} 个主搜索条件组合")
    print("前5个组合:")
    for i, combo in enumerate(main_combinations[:5]):
        print(f"  {i+1}. {combo}")
    print()

def main():
    """主测试函数"""
    print("开始测试重构后的position_list.py功能\n")

    try:
        test_extract_all_positions()
        test_generate_main_combinations()

        print("基础功能测试完成！")

    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
