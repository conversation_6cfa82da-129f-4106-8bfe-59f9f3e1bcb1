import json
import time
from typing import Dict, List, Any, Optional

import requests
from loguru import logger
from resx.config import *
from resx.mysql_dao import MySQLDao
from resx.redis_types import Redis
from urllib3 import disable_warnings

from position import positions, hot_city, conditions

disable_warnings()
redis = Redis(**CFG_REDIS_GS, db=3)
dao = MySQLDao(**CFG_MYSQL_GS_TEST, db_tb_name='prism.boss_position_list', primary_index_fields=(['job_name', 'brand_name'], []))

REDIS_PROGRESS_KEY = "boss_crawl_progress"
REDIS_COOKIES_KEY = "boss_cookies"
REDIS_EMPTY_COMBINATIONS_KEY = "boss_empty_combinations"
MAX_COOKIE_USAGE = 3

# 配置参数
ENABLE_REFINED_SEARCH = True
MAX_COMBINATION_SIZE = 3
ENABLE_DEGREE_COMBINATION = True
ENABLE_EXPERIENCE_COMBINATION = True
ENABLE_SALARY_COMBINATION = True

headers = {
    "referer": "https://www.zhipin.com/web/geek/jobs?query=%E7%88%AC%E8%99%AB",
    "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
}
url = "https://www.zhipin.com/wapi/zpgeek/search/joblist.json"

base_params = {
    "page": "1",
    "pageSize": "30",
    "query": "",
    "expectInfo": "",
    "multiSubway": "",
    "multiBusinessDistrict": "",
    "scene": "1",
}


def get_cookies_from_redis() -> Optional[Dict[str, Any]]:
    """从Redis获取cookies"""
    try:
        cookie_data = redis.lpop(REDIS_COOKIES_KEY)
        if cookie_data:
            return json.loads(cookie_data)
        else:
            logger.warning("Redis中没有可用的cookies")
            return None
    except Exception as e:
        logger.error(f"从Redis获取cookies失败: {e}")
        return None


def save_progress_to_redis(progress_data: Dict[str, Any]) -> None:
    """保存进度到Redis"""
    try:
        redis.set(REDIS_PROGRESS_KEY, json.dumps(progress_data, ensure_ascii=False))
        logger.info(f"进度已保存: {progress_data}")
    except Exception as e:
        logger.error(f"保存进度到Redis失败: {e}")


def get_progress_from_redis() -> Optional[Dict[str, Any]]:
    """从Redis获取进度"""
    try:
        progress_data = redis.get(REDIS_PROGRESS_KEY)
        if progress_data:
            return json.loads(progress_data)
        return None
    except Exception as e:
        logger.error(f"从Redis获取进度失败: {e}")
        return None


def generate_combination_cache_key(combination: Dict[str, Any]) -> str:
    """生成参数组合的缓存键"""
    # 创建一个排序后的参数字符串，确保相同参数组合生成相同的键
    sorted_params = sorted(combination.items())
    param_str = "&".join([f"{k}={v}" for k, v in sorted_params])

    # 使用哈希值作为缓存键，避免键过长
    import hashlib
    cache_key = hashlib.md5(param_str.encode('utf-8')).hexdigest()
    return cache_key


def is_combination_cached_as_empty(combination: Dict[str, Any]) -> bool:
    """检查参数组合是否已被缓存为无结果"""
    try:
        cache_key = generate_combination_cache_key(combination)
        return redis.sismember(REDIS_EMPTY_COMBINATIONS_KEY, cache_key)
    except Exception as e:
        logger.error(f"检查缓存失败: {e}")
        return False


def cache_empty_combination(combination: Dict[str, Any]) -> None:
    """将无结果的参数组合存储到Redis缓存"""
    try:
        cache_key = generate_combination_cache_key(combination)
        redis.sadd(REDIS_EMPTY_COMBINATIONS_KEY, cache_key)
        logger.debug(f"缓存无结果组合: {cache_key}")
    except Exception as e:
        logger.error(f"缓存组合失败: {e}")


def get_empty_combinations_count() -> int:
    """获取缓存中无结果组合的数量"""
    try:
        return redis.scard(REDIS_EMPTY_COMBINATIONS_KEY)
    except Exception as e:
        logger.error(f"获取缓存数量失败: {e}")
        return 0


def clear_empty_combinations_cache() -> bool:
    """清理无结果组合的缓存"""
    try:
        redis.delete(REDIS_EMPTY_COMBINATIONS_KEY)
        logger.info("已清理无结果组合缓存")
        return True
    except Exception as e:
        logger.error(f"清理缓存失败: {e}")
        return False


def get_position_category(position_code: int) -> str:
    """获取职位类别"""
    # 技术开发类
    tech_dev_positions = [100101, 100102, 100103, 100109, 100106, 100107, 100116, 100114, 100121,
                          100124, 100125, 100123, 100199, 100901, 100202, 100203, 100209, 100211,
                          100210, 100212, 100208, 100213]

    # 高级技术类（算法、架构等）
    senior_tech_positions = [101306, 100117, 101310, 100104, 101311, 101312, 100118, 100115,
                             101305, 101309, 101313, 100120, 101307, 101301, 101302, 101308,
                             100704, 100702, 100705, 100707, 100706]

    # 测试运维类
    test_ops_positions = [100301, 100309, 100302, 100303, 100305, 100308, 100307, 100304, 100310,
                          100401, 100405, 100403, 100407, 100404, 100402, 100406, 100409, 100408, 100410]

    # 数据分析类
    data_positions = [100511, 100508, 100507, 100506, 100512, 100514, 100122, 100515]

    # 销售市场类
    sales_marketing_positions = [140301, 140310, 140314, 140307, 140304, 140317, 140305, 140316, 140302,
                                 140101, 140111, 140109, 140104, 130109, 140115, 130111, 130122, 130104]

    # 管理类
    management_positions = [100601, 100603, 150104, 150403, 150108, 150102, 150103, 150105, 150109]

    # 产品运营类
    product_ops_positions = [110101, 110108, 110302, 110106, 110110, 110105, 110103, 130101, 130102,
                             130103, 130106, 130108, 130110, 130118]

    if position_code in tech_dev_positions:
        return "tech_dev"
    elif position_code in senior_tech_positions:
        return "senior_tech"
    elif position_code in test_ops_positions:
        return "test_ops"
    elif position_code in data_positions:
        return "data"
    elif position_code in sales_marketing_positions:
        return "sales_marketing"
    elif position_code in management_positions:
        return "management"
    elif position_code in product_ops_positions:
        return "product_ops"
    else:
        return "other"


def validate_combination(combination: Dict[str, Any]) -> bool:
    """验证搜索组合的合理性"""
    position_code = combination['position']
    experience_code = combination.get('experience', '')
    salary_code = combination.get('salary', '')
    degree_code = combination.get('degree', '')
    job_type_code = combination.get('jobType', '')

    # 如果参数为空字符串，跳过相关验证
    if experience_code == '' or salary_code == '' or degree_code == '' or job_type_code == '':
        return True

    position_category = get_position_category(position_code)

    # 1. 薪资与经验的匹配性
    # 应届生(102)和在校生(108)不应该有高薪
    if experience_code in [102, 108] and salary_code in [406, 407]:  # 20K+薪资
        return False

    # 1年以内经验不应该有超高薪
    if experience_code == 103 and salary_code == 407:  # 50K+薪资
        return False

    # 10年以上经验不应该有低薪
    if experience_code == 107 and salary_code in [402, 403]:  # 5K以下薪资
        return False

    # 2. 学历与职位的匹配性
    # 高级技术职位通常需要本科以上学历
    if position_category == "senior_tech" and degree_code in [209, 208, 206]:  # 高中及以下
        return False

    # 算法、数据类职位通常需要本科以上学历
    if position_category == "data" and degree_code in [209, 208, 206]:
        return False

    # 管理类职位通常需要大专以上学历
    if position_category == "management" and degree_code in [209, 208]:  # 初中及以下、中专/中技
        return False

    # 3. 经验与学历的匹配性
    # 10年以上经验但只有高中及以下学历不合理
    if experience_code == 107 and degree_code in [209, 208, 206]:
        return False

    # 5年以上经验但只有初中学历不合理
    if experience_code in [106, 107] and degree_code == 209:
        return False

    # 4. 职位与工作类型的匹配性
    # 高级技术职位、管理职位不适合兼职
    if job_type_code == 1903 and position_category in ["senior_tech", "management"]:  # 兼职
        return False

    # 5. 薪资与学历的匹配性
    # 高薪职位与低学历不匹配
    if salary_code in [406, 407] and degree_code in [209, 208, 206]:  # 20K+薪资配高中及以下学历
        return False

    # 6. 避免过于严格的条件组合
    # 博士学历 + 应届生 + 高薪的组合虽然理论可能，但实际很少
    if degree_code == 205 and experience_code in [102, 108] and salary_code in [406, 407]:
        return False

    return True


def extract_all_positions() -> List[Dict[str, Any]]:
    """从嵌套的positions结构中提取所有职位的平铺列表"""
    all_positions = []

    def extract_positions_recursive(position_list):
        for item in position_list:
            if 'subLevelModelList' in item:
                # 如果有子级列表，递归提取
                extract_positions_recursive(item['subLevelModelList'])
            else:
                # 叶子节点，添加到结果列表
                all_positions.append({'code': item['code'], 'name': item['name']})

    extract_positions_recursive(positions)
    return all_positions


def generate_main_combinations() -> List[Dict[str, Any]]:
    """生成主搜索条件组合：position × city × jobType"""
    all_positions = extract_all_positions()
    job_types = [jt for jt in conditions['jobTypeList'] if jt['code'] in [1901, 1903]]  # 全职和兼职

    main_combinations = []

    for position in all_positions:
        for city in hot_city:
            for job_type in job_types:
                combination = {
                    'position': position['code'],
                    'city': city['code'],
                    'jobType': job_type['code'],
                    'experience': '',
                    'salary': '',
                    'degree': '',
                    'stage': '',
                    'scale': '',
                }
                main_combinations.append(combination)

    logger.info(f"生成主搜索条件组合: {len(all_positions)} 个职位 × {len(hot_city)} 个城市 × {len(job_types)} 个工作类型 = {len(main_combinations)} 个组合")
    return main_combinations


def is_degree_applicable_for_position(position_code: int, degree_code: int) -> bool:
    """判断学历条件是否适用于特定职位"""
    position_name = next((p['name'] for p in extract_all_positions() if p['code'] == position_code), '')

    # 博士学历限制
    if degree_code == 205:  # 博士
        # 技术管理类：技术经理、架构师、技术总监、CTO/CIO、技术合伙人、总裁/总经理/CEO
        tech_management_positions = [100701, 100704, 100702, 100705, 100707, 150104, 150403, 150108]

        # 数据算法类：数据分析师、数据开发、数据挖掘、数据架构师、深度学习、机器学习、高性能计算工程师、自动驾驶系统工程师
        data_algorithm_positions = [100511, 100508, 100104, 100512, 101302, 101301, 101313, 101308]

        # 硬件工程类：电子工程师、硬件工程师、射频工程师、系统集成、光学工程师、数字前端设计师、集成电路IC设计
        hardware_positions = [101401, 100801, 100816, 100807, 100818, 101411, 101405]

        # 医药研发类：医药项目经理、医学编辑、医药代表、医疗器械销售等
        medical_positions = [100608, 210101, 210502, 210506]

        # 职位名称包含"AI"、"算法"关键字
        if 'AI' in position_name or '算法' in position_name:
            return True

        return position_code in tech_management_positions + data_algorithm_positions + hardware_positions + medical_positions

    # 低学历限制：仅对以下职位分类应用低学历条件（初中及以下209、中专/中技208、高中206）
    if degree_code in [209, 208, 206]:  # 初中及以下、中专/中技、高中
        position_category = get_position_category(position_code)
        # 客服/运营、销售、零售/生活服务、餐饮、酒店/旅游、直播/影视/传媒、物流/仓储/司机、采购/贸易
        low_education_categories = ["sales_marketing", "other"]
        return position_category in low_education_categories

    # 默认学历范围：大专202、本科203、硕士204
    if degree_code in [202, 203, 204]:
        return True

    return False


def generate_refined_combinations(main_combination: Dict[str, Any]) -> List[Dict[str, Any]]:
    """基于主条件生成细化搜索组合"""
    if not ENABLE_REFINED_SEARCH:
        return []

    position_code = main_combination['position']

    # 获取所有参数，排除code为0的选项
    all_experience = [exp for exp in conditions['experienceList'] if exp['code'] != 0] if ENABLE_EXPERIENCE_COMBINATION else []
    all_salary = [sal for sal in conditions['salaryList'] if sal['code'] != 0] if ENABLE_SALARY_COMBINATION else []
    all_degree = [deg for deg in conditions['degreeList'] if deg['code'] != 0] if ENABLE_DEGREE_COMBINATION else []
    all_stage = [st for st in conditions['stageList'] if st['code'] != 0]
    all_scale = [sc for sc in conditions['scaleList'] if sc['code'] != 0]

    # 生成所有可能的参数组合
    from itertools import combinations

    refined_combinations = []

    # 生成1到MAX_COMBINATION_SIZE维度的组合
    for combination_size in range(1, min(MAX_COMBINATION_SIZE + 1, 6)):  # 最多5个参数
        param_groups = []

        if ENABLE_EXPERIENCE_COMBINATION and all_experience:
            param_groups.append(('experience', all_experience))
        if ENABLE_SALARY_COMBINATION and all_salary:
            param_groups.append(('salary', all_salary))
        if ENABLE_DEGREE_COMBINATION and all_degree:
            param_groups.append(('degree', all_degree))
        param_groups.append(('stage', all_stage))
        param_groups.append(('scale', all_scale))

        # 生成参数组的组合
        for param_combination in combinations(param_groups, combination_size):
            # 生成具体的参数值组合
            def generate_param_combinations(param_list, index=0):
                if index >= len(param_list):
                    return [{}]

                param_name, param_values = param_list[index]
                sub_combinations = generate_param_combinations(param_list, index + 1)

                result = []
                for param_value in param_values:
                    for sub_combo in sub_combinations:
                        new_combo = sub_combo.copy()
                        new_combo[param_name] = param_value['code']
                        result.append(new_combo)

                return result

            param_value_combinations = generate_param_combinations(list(param_combination))

            # 生成最终的搜索组合
            for param_combo in param_value_combinations:
                # 检查学历条件的适用性
                if 'degree' in param_combo:
                    if not is_degree_applicable_for_position(position_code, param_combo['degree']):
                        continue

                # 创建完整的搜索组合
                refined_combo = main_combination.copy()
                for param_name, param_value in param_combo.items():
                    refined_combo[param_name] = param_value

                # 填充未设置的参数为空字符串
                for param_name in ['experience', 'salary', 'degree', 'stage', 'scale']:
                    if param_name not in param_combo:
                        refined_combo[param_name] = ''

                # 验证组合的合理性
                if validate_combination(refined_combo):
                    refined_combinations.append(refined_combo)

    logger.debug(f"为主条件 {main_combination} 生成了 {len(refined_combinations)} 个细化组合")
    return refined_combinations


def fetch_job_list(cookies: Dict[str, str], IP: str, search_params: Dict[str, Any]) -> tuple:
    """获取职位列表数据，返回(是否成功, 结果数量)"""
    max_retries = 3

    for retry in range(max_retries):
        try:
            params = base_params.copy()
            params.update(search_params)
            params["_"] = str(int(time.time() * 1000))

            session = requests.Session()
            session.proxies = {'http': IP, 'https': IP}

            response = session.get(url, headers=headers, verify=False, timeout=10, cookies=cookies, params=params)

            if response.status_code != 200:
                logger.warning(f"请求失败，状态码: {response.status_code}, 重试 {retry + 1}/{max_retries}")
                continue

            data = response.json()

            if 'zpData' not in data or 'jobList' not in data['zpData']:
                logger.warning(f"响应数据格式异常: {data}, 重试 {retry + 1}/{max_retries}")
                continue

            job_list = data['zpData']['jobList']

            if not job_list:
                logger.info("当前搜索条件下没有职位数据，将组合缓存为无结果")
                cache_empty_combination(search_params)
                return True, 0

            # 保存数据
            for info in job_list:
                try:
                    job_data = {
                        'job_name': info.get('jobName', ''),
                        'brand_name': info.get('brandName', ''),
                        'salary_desc': info.get('salaryDesc', ''),
                        'job_experience': info.get('jobExperience', ''),
                        'job_degree': info.get('jobDegree', ''),
                        'city_name': info.get('cityName', ''),
                        'skills': ', '.join(info.get('skills', [])),
                        'brand_industry': info.get('brandIndustry', ''),
                        'brand_scale_name': info.get('brandScaleName', ''),
                        'area_district': info.get('areaDistrict', ''),
                        'business_district': info.get('businessDistrict', ''),
                        'industry': info.get('industry', ''),
                        'security_id': info.get('securityId', ''),
                        'lid': info.get('lid', ''),
                    }
                    dao.save(job_data)
                    logger.debug(f"保存职位数据: {job_data['job_name']} - {job_data['brand_name']}")
                except Exception as e:
                    logger.error(f"保存单条数据失败: {e}")

            logger.info(f"成功获取并保存 {len(job_list)} 条职位数据")
            return True, len(job_list)

        except Exception as e:
            logger.error(f"请求失败 (重试 {retry + 1}/{max_retries}): {e}")
            if retry < max_retries - 1:
                time.sleep(2)  # 重试前等待

    return False, 0


def main():
    """主函数：两阶段搜索逻辑"""
    logger.info("开始Boss职位数据抓取任务")

    # 生成主搜索条件组合
    main_combinations = generate_main_combinations()
    logger.info(f"生成主搜索条件组合: {len(main_combinations)} 个")

    # 获取上次的进度
    progress = get_progress_from_redis()
    start_index = 0

    if progress:
        start_index = progress.get('current_index', 0)
        logger.info(f"从上次进度继续: 第 {start_index} 个主组合")
    else:
        logger.info("开始新的抓取任务")

    current_cookie_data = None
    cookie_usage_count = 0

    # 统计信息
    skipped_count = 0
    cached_empty_count = get_empty_combinations_count()
    main_empty_count = 0
    main_full_count = 0
    main_partial_count = 0
    refined_search_count = 0

    logger.info(f"当前缓存中有 {cached_empty_count} 个无结果组合")

    for i in range(start_index, len(main_combinations)):
        main_combination = main_combinations[i]

        # 检查是否需要获取新的cookies
        if current_cookie_data is None or cookie_usage_count >= MAX_COOKIE_USAGE:
            current_cookie_data = get_cookies_from_redis()
            if current_cookie_data is None:
                logger.error("无法获取cookies，等待30秒后重试")
                time.sleep(30)
                continue
            cookie_usage_count = 0
            logger.info(f"获取新的cookies，IP: {current_cookie_data['IP']}")

        # 保存当前进度
        progress_data = {
            'current_index': i,
            'total_combinations': len(main_combinations),
            'current_combination': main_combination,
            'timestamp': time.time()
        }
        save_progress_to_redis(progress_data)

        # 获取组合的可读名称用于日志
        all_positions = extract_all_positions()
        position_name = next((p['name'] for p in all_positions if p['code'] == main_combination['position']), '未知职位')
        city_name = next((c['name'] for c in hot_city if c['code'] == main_combination['city']), '未知城市')
        job_type_name = next((jt['name'] for jt in conditions['jobTypeList'] if jt['code'] == main_combination['jobType']), '未知类型')

        logger.info(f"处理第 {i + 1}/{len(main_combinations)} 个主组合: {city_name} - {position_name} - {job_type_name}")

        # 检查主组合是否已被缓存为无结果
        if is_combination_cached_as_empty(main_combination):
            skipped_count += 1
            logger.info(f"跳过已缓存的无结果主组合，累计跳过: {skipped_count}")
            continue

        # 执行主搜索条件搜索
        success, result_count = fetch_job_list(
            current_cookie_data['cookies'],
            current_cookie_data['IP'],
            main_combination
        )

        if not success:
            logger.error(f"主组合搜索失败，将获取新的cookies")
            current_cookie_data = None
            cookie_usage_count = MAX_COOKIE_USAGE
            continue

        cookie_usage_count += 1

        # 根据搜索结果判断是否进行细化搜索
        if result_count == 0:
            main_empty_count += 1
            logger.info(f"主组合无结果，已缓存")
        elif result_count == 30:
            main_full_count += 1
            logger.info(f"主组合满页结果({result_count}条)，开始细化搜索")

            # 生成细化搜索组合
            refined_combinations = generate_refined_combinations(main_combination)

            for refined_combo in refined_combinations:
                # 检查是否需要获取新的cookies
                if current_cookie_data is None or cookie_usage_count >= MAX_COOKIE_USAGE:
                    current_cookie_data = get_cookies_from_redis()
                    if current_cookie_data is None:
                        logger.error("无法获取cookies，等待30秒后重试")
                        time.sleep(30)
                        break
                    cookie_usage_count = 0
                    logger.info(f"获取新的cookies，IP: {current_cookie_data['IP']}")

                # 检查细化组合是否已被缓存为无结果
                if is_combination_cached_as_empty(refined_combo):
                    continue

                # 执行细化搜索
                refined_success, refined_count = fetch_job_list(
                    current_cookie_data['cookies'],
                    current_cookie_data['IP'],
                    refined_combo
                )

                if not refined_success:
                    logger.error(f"细化搜索失败")
                    current_cookie_data = None
                    cookie_usage_count = MAX_COOKIE_USAGE
                    break

                cookie_usage_count += 1
                refined_search_count += 1

                logger.debug(f"细化搜索结果: {refined_count} 条")

                # 如果细化搜索结果少于30条，终止该主条件的后续细化搜索
                if refined_count < 30:
                    logger.info(f"细化搜索结果不满页({refined_count}条)，终止该主条件的后续细化搜索")
                    break

                time.sleep(1)  # 细化搜索间隔
        else:
            main_partial_count += 1
            logger.info(f"主组合部分结果({result_count}条)，不进行细化搜索")

        time.sleep(2)  # 主搜索间隔

    logger.info("所有主搜索组合处理完成")

    # 显示统计信息
    final_cached_count = get_empty_combinations_count()
    new_cached_count = final_cached_count - cached_empty_count
    logger.info(f"=== 搜索统计 ===")
    logger.info(f"主组合总数: {len(main_combinations)}")
    logger.info(f"主组合无结果: {main_empty_count}")
    logger.info(f"主组合满页结果: {main_full_count}")
    logger.info(f"主组合部分结果: {main_partial_count}")
    logger.info(f"细化搜索次数: {refined_search_count}")
    logger.info(f"跳过的缓存组合: {skipped_count}")
    logger.info(f"新增缓存的无结果组合: {new_cached_count}")
    logger.info(f"缓存中总的无结果组合数: {final_cached_count}")

    # 清除进度记录
    redis.delete(REDIS_PROGRESS_KEY)


if __name__ == '__main__':
    main()
